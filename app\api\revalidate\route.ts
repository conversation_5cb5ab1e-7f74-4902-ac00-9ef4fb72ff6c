// 改进的混合刷新机制
// app/api/revalidate/route.ts

import { NextRequest, NextResponse } from 'next/server'
import { revalidateTag } from 'next/cache'

export async function POST(request: NextRequest) {
  const secret = request.nextUrl.searchParams.get('secret')

  // 验证密钥
  if (secret !== process.env.REVALIDATE_SECRET) {
    return NextResponse.json({ message: 'Invalid Token' }, { status: 401 })
  }

  try {
    const { tag, action } = await request.json()

    // 根据不同操作类型清除相应缓存
    switch (action) {
      case 'tutorial_updated':
        revalidateTag('tutorials')
        revalidateTag('public-tutorials')
        break
      case 'key_verified':
        revalidateTag('user-unlocks')
        break
      case 'tutorial_created':
        revalidateTag('tutorials')
        revalidateTag('categories')
        break
      default:
        if (tag) {
          revalidateTag(tag)
        }
    }

    console.log(`✅ Cache revalidated: ${tag || action}`)
    return NextResponse.json({ 
      revalidated: true, 
      action: action || tag,
      timestamp: Date.now() 
    })

  } catch (error) {
    console.error('❌ Revalidation failed:', error)
    return NextResponse.json(
      { message: 'Revalidation failed' }, 
      { status: 500 }
    )
  }
}
"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import { 
  Key, 
  ChevronDown, 
  ChevronRight, 
  Download, 
  Plus, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Copy,
  Trash2,
  RefreshCw
} from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface Tutorial {
  id: number
  title: string
  status: string
  created_at: string
  total_keys?: number
  used_keys?: number
  exported_keys?: number
  available_keys?: number
  unused_keys?: number
}

interface TutorialKey {
  id: number
  key_code: string
  status: string
  created_at: string
  used_at: string | null
}

interface TutorialKeysManagerProps {
  tutorials: Tutorial[]
  onDataUpdate: () => void
}

export function TutorialKeysManager({ tutorials, onDataUpdate }: TutorialKeysManagerProps) {
  const [keyStats, setKeyStats] = useState<Tutorial[]>([])
  const [expandedTutorials, setExpandedTutorials] = useState<Set<number>>(new Set())
  const [tutorialKeys, setTutorialKeys] = useState<{ [key: number]: { keys: TutorialKey[], pagination: any } }>({})
  const [keyPages, setKeyPages] = useState<{ [key: number]: number }>({}) // 记录每个教程当前页码
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()

  // 密钥生成状态
  const [generateDialog, setGenerateDialog] = useState(false)
  const [generateForm, setGenerateForm] = useState({
    tutorial_id: "",
    count: "10"
  })

  // 批量导出状态
  const [exportDialog, setExportDialog] = useState(false)
  const [exportForm, setExportForm] = useState({
    tutorial_id: "",
    count: "50"
  })

  useEffect(() => {
    loadKeyStats()
  }, [])

  const loadKeyStats = async (forceRefresh: boolean = false) => {
    try {
      setLoading(true)
      const timestamp = forceRefresh ? `?_t=${Date.now()}` : ''
      const response = await fetch(`/api/admin/keys${timestamp}`, {
        headers: forceRefresh ? {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        } : {}
      })
      
      if (response.ok) {
        const data = await response.json()
        setKeyStats(data.data || [])
        console.log(`✅ 已${forceRefresh ? '强制' : ''}加载密钥统计信息`)
      } else {
        toast({
          title: "加载失败",
          description: "无法加载密钥统计信息",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Load key stats error:", error)
      toast({
        title: "加载错误",
        description: "请检查网络连接",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const loadTutorialKeys = async (tutorialId: number, page: number = 1, forceRefresh: boolean = false) => {
    try {
      const timestamp = forceRefresh ? `&_t=${Date.now()}` : ''
      const response = await fetch(`/api/admin/keys/${tutorialId}?page=${page}&pageSize=5${timestamp}`, {
        headers: forceRefresh ? {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        } : {}
      })
      
      if (response.ok) {
        const data = await response.json()
        setTutorialKeys(prev => ({
          ...prev,
          [tutorialId]: {
            keys: data.data.keys || [],
            pagination: data.data.pagination || {}
          }
        }))
        setKeyPages(prev => ({
          ...prev,
          [tutorialId]: page
        }))
        console.log(`✅ 已${forceRefresh ? '强制' : ''}加载教程 ${tutorialId} 的密钥列表`)
      }
    } catch (error) {
      console.error("Load tutorial keys error:", error)
    }
  }

  const toggleTutorial = (tutorialId: number) => {
    const newExpanded = new Set(expandedTutorials)
    if (newExpanded.has(tutorialId)) {
      newExpanded.delete(tutorialId)
    } else {
      newExpanded.add(tutorialId)
      // 首次展开时加载密钥详情
      if (!tutorialKeys[tutorialId]) {
        loadTutorialKeys(tutorialId)
      }
    }
    setExpandedTutorials(newExpanded)
  }

  const generateKeys = async () => {
    if (!generateForm.tutorial_id || !generateForm.count) {
      toast({
        title: "请填写完整信息",
        variant: "destructive"
      })
      return
    }

    try {
      setLoading(true)
      const response = await fetch("/api/admin/keys/generate", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          tutorial_id: parseInt(generateForm.tutorial_id),
          count: parseInt(generateForm.count)
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "密钥生成成功",
          description: `已生成 ${data.data.generated_count} 个密钥`
        })
        setGenerateDialog(false)
        setGenerateForm({ tutorial_id: "", count: "10" })
        
        // 强制刷新数据
        await loadKeyStats(true)
        onDataUpdate()
        
        // 如果当前教程已展开，重新加载其密钥列表
        const tutorialId = parseInt(generateForm.tutorial_id)
        if (expandedTutorials.has(tutorialId)) {
          await loadTutorialKeys(tutorialId, 1, true) // 强制刷新
        }
      } else {
        toast({
          title: "生成失败",
          description: data.error || "请检查输入信息",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Generate keys error:", error)
      toast({
        title: "生成错误",
        description: "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const exportKeys = async () => {
    if (!exportForm.tutorial_id || !exportForm.count) {
      toast({
        title: "请填写完整信息",
        variant: "destructive"
      })
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/keys/${exportForm.tutorial_id}/export`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          count: parseInt(exportForm.count)
        })
      })

      const data = await response.json()

      if (response.ok && data.success) {
        // 创建下载文件
        const blob = new Blob([data.data.keys], { type: 'text/plain' })
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${data.data.tutorial_title}-keys-${new Date().toISOString().split('T')[0]}.txt`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)

        toast({
          title: "导出成功",
          description: `已导出 ${data.data.count} 个密钥`
        })
        
        setExportDialog(false)
        setExportForm({ tutorial_id: "", count: "50" })
        loadKeyStats()
        // 刷新当前展开教程的密钥列表
        if (expandedTutorials.has(parseInt(exportForm.tutorial_id))) {
          loadTutorialKeys(parseInt(exportForm.tutorial_id))
        }
      } else {
        toast({
          title: "导出失败",
          description: data.error || "请检查输入信息",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Export keys error:", error)
      toast({
        title: "导出错误",
        description: "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const copyKeyToClipboard = (keyCode: string) => {
    navigator.clipboard.writeText(keyCode)
    toast({
      title: "复制成功",
      description: "密钥已复制到剪贴板"
    })
  }

  const deleteKey = async (keyId: number, keyCode: string, tutorialId: number) => {
    if (!confirm(`确认删除密钥 "${keyCode}" 吗？\n\n注意：\n- 如果此密钥已被用户使用，相关的解锁记录也会被删除\n- 用户将失去对应教程的访问权限\n- 此操作不可撤销`)) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/keys/delete?keyId=${keyId}`, {
        method: "DELETE"
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "删除成功",
          description: `密钥 ${keyCode} 已删除${data.data.deleted_unlocks_count > 0 ? `，同时删除了 ${data.data.deleted_unlocks_count} 条解锁记录` : ''}`
        })
        
        // 刷新统计数据
        loadKeyStats()
        // 刷新当前教程的密钥列表
        loadTutorialKeys(tutorialId, keyPages[tutorialId] || 1)
        // 通知父组件数据已更新
        onDataUpdate()
      } else {
        toast({
          title: "删除失败",
          description: data.error || "请稍后重试",
          variant: "destructive"
        })
        // 如果有详细错误信息，也输出到控制台
        if (data.details) {
          console.error("删除失败详情:", data.details)
        }
      }
    } catch (error) {
      console.error("Delete key error:", error)
      toast({
        title: "删除错误",
        description: "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const clearTutorialKeys = async (tutorialId: number, tutorialTitle: string) => {
    if (!confirm(`确认清除教程"${tutorialTitle}"的所有未使用密钥吗？\n\n注意：\n- 已使用的密钥将被保留\n- 此操作不可撤销`)) {
      return
    }

    try {
      setLoading(true)
      const response = await fetch(`/api/admin/keys/${tutorialId}/clear`, {
        method: "DELETE"
      })

      const data = await response.json()

      if (response.ok && data.success) {
        toast({
          title: "清除成功",
          description: `已清除 ${data.data.deleted_count} 个密钥，保留 ${data.data.preserved_used_keys} 个已使用密钥`
        })
        
        // 刷新统计数据
        loadKeyStats()
        // 如果该教程正在展开，也刷新密钥列表
        if (expandedTutorials.has(tutorialId)) {
          loadTutorialKeys(tutorialId)
        }
      } else {
        toast({
          title: "清除失败",
          description: data.error || "请稍后重试",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error("Clear keys error:", error)
      toast({
        title: "清除错误",
        description: "请稍后重试",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'unused':
        return <Badge variant="default">未使用</Badge>
      case 'used':
        return <Badge variant="destructive">已使用</Badge>
      case 'exported':
        return <Badge variant="secondary">已导出</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="space-y-4">
      {/* 操作按钮栏 */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-2">
          <Dialog open={generateDialog} onOpenChange={setGenerateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                生成密钥
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>生成新密钥</DialogTitle>
                <DialogDescription>为指定教程生成验证密钥</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="generate-tutorial">选择教程</Label>
                  <select
                    id="generate-tutorial"
                    className="w-full p-2 border rounded"
                    value={generateForm.tutorial_id}
                    onChange={(e) => setGenerateForm({ ...generateForm, tutorial_id: e.target.value })}
                  >
                    <option value="">请选择教程</option>
                    {keyStats.filter(t => t.status === 'published').map(tutorial => (
                      <option key={tutorial.id} value={tutorial.id.toString()}>
                        {tutorial.title}
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="generate-count">生成数量</Label>
                  <Input
                    id="generate-count"
                    type="number"
                    min="1"
                    max="100"
                    value={generateForm.count}
                    onChange={(e) => setGenerateForm({ ...generateForm, count: e.target.value })}
                  />
                </div>
                <Button onClick={generateKeys} disabled={loading} className="w-full">
                  {loading ? "生成中..." : "生成密钥"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={exportDialog} onOpenChange={setExportDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                批量导出
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>批量导出密钥</DialogTitle>
                <DialogDescription>导出指定数量的未使用密钥，导出后密钥将不再显示在列表中</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="export-tutorial">选择教程</Label>
                  <select
                    id="export-tutorial"
                    className="w-full p-2 border rounded"
                    value={exportForm.tutorial_id}
                    onChange={(e) => setExportForm({ ...exportForm, tutorial_id: e.target.value })}
                  >
                    <option value="">请选择教程</option>
                    {keyStats.filter(t => (t.unused_keys || 0) > 0).map(tutorial => (
                      <option key={tutorial.id} value={tutorial.id.toString()}>
                        {tutorial.title} (可导出: {tutorial.unused_keys})
                      </option>
                    ))}
                  </select>
                </div>
                <div>
                  <Label htmlFor="export-count">导出数量</Label>
                  <Input
                    id="export-count"
                    type="number"
                    min="1"
                    max="1000"
                    value={exportForm.count}
                    onChange={(e) => setExportForm({ ...exportForm, count: e.target.value })}
                  />
                </div>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
                  <div className="flex items-start">
                    <AlertCircle className="h-4 w-4 text-orange-600 mr-2 mt-0.5" />
                    <div className="text-sm text-orange-700">
                      <p>导出说明：</p>
                      <ul className="list-disc list-inside mt-1 space-y-1">
                        <li>导出的密钥将标记为"已导出"状态</li>
                        <li>已导出的密钥不会在管理界面显示</li>  
                        <li>导出文件格式：每行一个密钥</li>
                        <li>确保密钥唯一性，即使重复导出</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <Button onClick={exportKeys} disabled={loading} className="w-full">
                  {loading ? "导出中..." : "确认导出"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        <Button variant="outline" onClick={loadKeyStats} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          {loading ? "刷新中..." : "刷新数据"}
        </Button>
      </div>

      {/* 教程密钥列表 */}
      <div className="space-y-2">
        {keyStats.map((tutorial) => (
          <Card key={tutorial.id}>
            <Collapsible>
              <CollapsibleTrigger
                onClick={() => toggleTutorial(tutorial.id)}
                className="w-full"
              >
                <CardHeader className="hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 text-left">
                      <CardTitle className="text-lg flex items-center">
                        {expandedTutorials.has(tutorial.id) ? 
                          <ChevronDown className="h-4 w-4 mr-2" /> : 
                          <ChevronRight className="h-4 w-4 mr-2" />
                        }
                        {tutorial.title}
                      </CardTitle>
                      <CardDescription>
                        创建时间: {new Date(tutorial.created_at).toLocaleDateString()}
                      </CardDescription>
                    </div>
                    <div className="flex items-center space-x-4 text-sm">
                      <div className="text-center">
                        <div className="font-medium text-blue-600">{tutorial.total_keys || 0}</div>
                        <div className="text-gray-500">总密钥</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-green-600">{tutorial.unused_keys || 0}</div>
                        <div className="text-gray-500">可用</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-red-600">{tutorial.used_keys || 0}</div>
                        <div className="text-gray-500">已兑换</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-gray-600">{tutorial.exported_keys || 0}</div>
                        <div className="text-gray-500">已导出</div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          clearTutorialKeys(tutorial.id, tutorial.title)
                        }}
                        disabled={loading || (tutorial.total_keys || 0) === 0}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        清除密钥
                      </Button>
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>
              
              <CollapsibleContent>
                {expandedTutorials.has(tutorial.id) && (
                  <CardContent className="pt-0">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium mb-3 flex items-center">
                        <Key className="h-4 w-4 mr-2" />
                        密钥列表 (未导出的密钥)
                      </h4>
                      
                      {tutorialKeys[tutorial.id] ? (
                        tutorialKeys[tutorial.id].keys.length > 0 ? (
                          <div>
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>密钥</TableHead>
                                  <TableHead>状态</TableHead>
                                  <TableHead>创建时间</TableHead>
                                  <TableHead>使用时间</TableHead>
                                  <TableHead>操作</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {tutorialKeys[tutorial.id].keys.map((key) => (
                                  <TableRow key={key.id}>
                                    <TableCell className="font-mono text-sm">{key.key_code}</TableCell>
                                    <TableCell>{getStatusBadge(key.status)}</TableCell>
                                    <TableCell>{new Date(key.created_at).toLocaleDateString()}</TableCell>
                                    <TableCell>
                                      {key.used_at ? new Date(key.used_at).toLocaleDateString() : "-"}
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex items-center space-x-1">
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => copyKeyToClipboard(key.key_code)}
                                          title="复制密钥"
                                        >
                                          <Copy className="h-3 w-3" />
                                        </Button>
                                        <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => deleteKey(key.id, key.key_code, tutorial.id)}
                                          disabled={loading}
                                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                          title="删除密钥"
                                        >
                                          <Trash2 className="h-3 w-3" />
                                        </Button>
                                      </div>
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                            
                            {/* 分页控件 */}
                            {tutorialKeys[tutorial.id].pagination && tutorialKeys[tutorial.id].pagination.total_pages > 1 && (
                              <div className="flex items-center justify-between mt-4 px-2">
                                <div className="text-sm text-gray-500">
                                  显示第 {((tutorialKeys[tutorial.id].pagination.current_page - 1) * tutorialKeys[tutorial.id].pagination.page_size) + 1} - {Math.min(tutorialKeys[tutorial.id].pagination.current_page * tutorialKeys[tutorial.id].pagination.page_size, tutorialKeys[tutorial.id].pagination.total_count)} 项，共 {tutorialKeys[tutorial.id].pagination.total_count} 项
                                </div>
                                <div className="flex items-center space-x-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={!tutorialKeys[tutorial.id].pagination.has_prev}
                                    onClick={() => loadTutorialKeys(tutorial.id, tutorialKeys[tutorial.id].pagination.current_page - 1)}
                                  >
                                    上一页
                                  </Button>
                                  <span className="text-sm">
                                    第 {tutorialKeys[tutorial.id].pagination.current_page} / {tutorialKeys[tutorial.id].pagination.total_pages} 页
                                  </span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={!tutorialKeys[tutorial.id].pagination.has_next}
                                    onClick={() => loadTutorialKeys(tutorial.id, tutorialKeys[tutorial.id].pagination.current_page + 1)}
                                  >
                                    下一页
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-8 text-gray-500">
                            <Key className="h-12 w-12 mx-auto mb-3 opacity-50" />
                            <p>暂无可显示的密钥</p>
                            <p className="text-sm">已导出的密钥不会在此显示</p>
                          </div>
                        )
                      ) : (
                        <div className="text-center py-4">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
                          <p className="text-sm text-gray-500 mt-2">加载中...</p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                )}
              </CollapsibleContent>
            </Collapsible>
          </Card>
        ))}
      </div>

      {keyStats.length === 0 && !loading && (
        <Card className="text-center py-12">
          <CardContent>
            <BarChart3 className="h-16 w-16 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 text-lg">暂无已发布的教程</p>
            <p className="text-gray-400">发布教程后即可生成和管理密钥</p>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
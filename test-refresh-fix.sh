#!/bin/bash

# 数据刷新问题修复验证脚本
# 验证所有关键API的无缓存机制是否工作正常

echo "🔍 开始验证数据刷新修复..."
echo "================================"

BASE_URL="http://localhost:3000"

echo "📊 1. 测试教程API无缓存机制"
echo "发送带时间戳的请求..."
RESPONSE1=$(curl -s -H "Cache-Control: no-cache" "${BASE_URL}/api/public/tutorials?_t=$(date +%s)")
echo "Response 1 length: $(echo $RESPONSE1 | wc -c)"

sleep 1

echo "发送第二个请求..."
RESPONSE2=$(curl -s -H "Cache-Control: no-cache" "${BASE_URL}/api/public/tutorials?_t=$(date +%s)")
echo "Response 2 length: $(echo $RESPONSE2 | wc -c)"

echo ""
echo "🔑 2. 测试管理后台密钥API无缓存机制"
KEY_RESPONSE=$(curl -s -H "Cache-Control: no-cache" "${BASE_URL}/api/admin/keys?_t=$(date +%s)")
echo "Admin keys response length: $(echo $KEY_RESPONSE | wc -c)"

echo ""
echo "👤 3. 测试用户解锁API无缓存机制"
USER_RESPONSE=$(curl -s -H "Cache-Control: no-cache" "${BASE_URL}/api/user-unlocks?_t=$(date +%s)")
echo "User unlocks response length: $(echo $USER_RESPONSE | wc -c)"

echo ""
echo "✅ 验证完成！"
echo "如果看到不同的时间戳和响应，说明无缓存机制正常工作。"
echo "如果数据仍然不更新，请检查：" 
echo "1. 开发服务器是否重启"
echo "2. 浏览器缓存是否清理"
echo "3. 数据库连接是否正常"
import { NextRequest, NextResponse } from "next/server"
import { createClient } from "@supabase/supabase-js"

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const tutorialId = searchParams.get('tutorialId')

    // 如果指定了 tutorialId，返回特定教程的密钥信息
    if (tutorialId) {
      const { data: keys, error } = await supabaseAdmin
        .from('tutorial_keys')
        .select('*')
        .eq('tutorial_id', parseInt(tutorialId))

      if (error) {
        console.error("Get tutorial keys error:", error)
        return NextResponse.json({ error: "Failed to fetch tutorial keys" }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        data: keys || []
      }, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
          'X-Timestamp': new Date().toISOString()
        }
      })
    }

    // 否则返回所有教程及其密钥统计信息
    const { data: tutorials, error: tutorialsError } = await supabaseAdmin
      .from('tutorials')
      .select(`
        id,
        title,
        status,
        created_at
      `)
      .eq('status', 'published')
      .order('created_at', { ascending: false })

    if (tutorialsError) {
      console.error("Get tutorials error:", tutorialsError)
      return NextResponse.json({ error: "Failed to fetch tutorials" }, { status: 500 })
    }

    // 为每个教程获取密钥统计信息
    const tutorialsWithKeyStats = await Promise.all(
      tutorials.map(async (tutorial) => {
        // 获取总密钥数
        const { count: totalKeys } = await supabaseAdmin
          .from('tutorial_keys')
          .select('*', { count: 'exact', head: true })
          .eq('tutorial_id', tutorial.id)

        // 获取已使用密钥数
        const { count: usedKeys } = await supabaseAdmin
          .from('tutorial_keys')
          .select('*', { count: 'exact', head: true })
          .eq('tutorial_id', tutorial.id)
          .eq('status', 'used')

        // 获取已导出密钥数
        const { count: exportedKeys } = await supabaseAdmin
          .from('tutorial_keys')
          .select('*', { count: 'exact', head: true })
          .eq('tutorial_id', tutorial.id)
          .eq('status', 'exported')

        // 获取可用密钥数
        const { count: availableKeys } = await supabaseAdmin
          .from('tutorial_keys')
          .select('*', { count: 'exact', head: true })
          .eq('tutorial_id', tutorial.id)
          .eq('status', 'unused')

        return {
          ...tutorial,
          total_keys: totalKeys || 0,
          used_keys: usedKeys || 0,
          exported_keys: exportedKeys || 0,
          available_keys: availableKeys || 0,
          unused_keys: (totalKeys || 0) - (usedKeys || 0) - (exportedKeys || 0)
        }
      })
    )

    return NextResponse.json({
      success: true,
      data: tutorialsWithKeyStats
    }, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Timestamp': new Date().toISOString()
      }
    })
  } catch (error) {
    console.error("Get keys error:", error)
    return NextResponse.json({ error: "Failed to fetch keys" }, { status: 500 })
  }
}

# 《奏事折》- 微信小程序广告联盟验证码系统

## 圣旨概要
皇上欲开发微信小程序，通过接入微信广告联盟实现盈利，核心机制为用户观看激励视频广告后获得验证码，验证码可用于验证其他产品或服务的使用权限。

## 技术可行性分析

### 1. 微信小程序广告技术支持
- ✅ 微信官方支持激励视频广告：`wx.createRewardedVideoAd()`
- ✅ 完整的广告生命周期回调机制
- ✅ 可通过 `onClose` 回调判断用户是否完整观看广告
- ✅ 广告收益结算机制成熟

### 2. 验证码系统技术要点
- 验证码生成：使用加密安全随机数，6-8位数字字母组合
- 存储机制：数据库存储，包含有效期、使用次数等字段
- 验证逻辑：检查有效期、使用次数、用户权限
- 唯一性保证：数据库唯一索引约束

### 3. 防刷机制设计
- IP限制：同一IP每日观看次数限制
- 用户限制：同一微信用户每日观看次数限制  
- 时间间隔：两次观看之间最小时间间隔
- 设备指纹：基于设备特征的防刷识别

## 技术方案对比

### 方案一：小程序 + 微信云开发
**技术栈：** 微信小程序 + 云函数 + 云数据库
**优势：**
- 与微信生态深度集成，开发简单
- 无需自建服务器，运维成本低
- 天然支持微信用户体系
**劣势：**
- 功能扩展受限于云开发能力
- 数据迁移困难
- 成本随用量线性增长

### 方案二：小程序 + 独立后端
**技术栈：** 微信小程序 + Node.js/Express + MongoDB
**优势：**
- 技术选型灵活，可扩展性强
- 便于集成第三方服务
- 数据完全自主控制
**劣势：**
- 需要自建服务器和运维
- 开发复杂度较高
- 需要处理微信登录对接

### 方案三：小程序 + Serverless
**技术栈：** 微信小程序 + 腾讯云函数 + 云数据库
**优势：**
- 按需付费，成本可控
- 自动扩缩容，无需运维
- 冷启动优化后性能可接受
**劣势：**
- 调试相对复杂
- 函数执行时间限制
- 依赖云厂商生态

## 核心技术实现要点

### 1. 广告观看验证流程
```javascript
// 小程序端
const rewardedVideoAd = wx.createRewardedVideoAd({
  adUnitId: 'adunit-xxx'
})

rewardedVideoAd.onClose((res) => {
  if (res.isEnded) {
    // 用户完整观看，请求后端生成验证码
    this.requestVerificationCode()
  }
})
```

### 2. 验证码数据模型
```javascript
{
  code: String,        // 验证码
  userId: String,      // 微信用户ID
  createTime: Date,    // 创建时间
  expireTime: Date,    // 过期时间
  usedCount: Number,   // 已使用次数
  maxUseCount: Number, // 最大使用次数
  status: String       // 状态：active/used/expired
}
```

### 3. 防刷限制策略
- 同一用户每日最多观看10次广告
- 同一IP每小时最多请求5次
- 两次观看间隔不少于30秒
- 异常行为检测和封禁机制

## 商业可行性评估

### 收益预期
- 激励视频广告 eCPM：10-50元/千次展示
- 需要日活用户1000+才能产生可观收益
- 用户留存和广告观看频次是关键指标

### 合规风险
- 需严格遵循微信小程序广告政策
- 验证码发放不能影响用户体验
- 避免诱导用户过度观看广告

### 开发周期估算
- 小程序前端开发：1-2周
- 后端API开发：1-2周  
- 测试优化：1周
- **总计：3-5周**

## 推荐方案
基于技术成熟度、开发效率和成本考虑，**推荐方案一（小程序+微信云开发）**作为MVP版本，后期可根据业务发展需要迁移到独立后端。

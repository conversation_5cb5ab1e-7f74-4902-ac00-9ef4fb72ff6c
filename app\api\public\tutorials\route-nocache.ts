import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

// ==========================================
// 教程API - 无缓存版本 (临时解决方案)
// 解决数据不更新问题
// ==========================================

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 教程API查询开始 - 无缓存版本')
    
    // 直接查询最新数据，无缓存
    const { data: tutorials, error, count } = await supabaseAdmin
      .from('tutorials')
      .select(`
        id,
        title,
        description,
        price,
        tags,
        created_at,
        updated_at,
        thumbnail_url,
        categories!inner(
          id,
          name,
          description
        )
      `, { count: 'exact' })
      .eq('status', 'published')
      .order('created_at', { ascending: false })

    if (error) {
      console.error('❌ Supabase查询错误:', error)
      throw error
    }

    // 格式化教程数据
    const formattedTutorials = tutorials?.map(tutorial => ({
      id: tutorial.id,
      title: tutorial.title,
      description: tutorial.description,
      price: tutorial.price,
      tags: tutorial.tags || [],
      created_at: tutorial.created_at,
      updated_at: tutorial.updated_at,
      thumbnail_url: tutorial.thumbnail_url,
      category: {
        id: tutorial.categories.id,
        name: tutorial.categories.name,
        description: tutorial.categories.description
      },
      // 向后兼容
      category_name: tutorial.categories.name
    })) || []

    const response = {
      success: true,
      data: formattedTutorials,
      pagination: {
        page: 1,
        limit: formattedTutorials.length,
        total: count || 0,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    }

    console.log(`✅ 返回 ${formattedTutorials.length} 个教程 (无缓存)`)

    // 强制无缓存响应
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'X-Cache-Strategy': 'no-cache',
        'X-Timestamp': new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('❌ 教程API异常:', error)
    
    return NextResponse.json({
      success: false,
      data: [],
      error: error instanceof Error ? error.message : '获取教程列表失败'
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Error': 'true'
      }
    })
  }
}
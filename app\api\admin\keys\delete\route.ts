import { NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

export async function DELETE(request: NextRequest) {
  try {
    console.log('=== 开始密钥删除操作 ===')
    
    const { searchParams } = new URL(request.url)
    const keyIdParam = searchParams.get('keyId')
    
    console.log('请求参数:', { keyIdParam, url: request.url })
    
    if (!keyIdParam) {
      console.log('❌ 缺少密钥ID参数')
      return NextResponse.json({ error: "缺少密钥ID参数" }, { status: 400 })
    }

    const keyId = parseInt(keyIdParam)
    
    if (isNaN(keyId)) {
      console.log('❌ 无效的密钥ID:', keyIdParam)
      return NextResponse.json({ error: "无效的密钥ID" }, { status: 400 })
    }

    console.log(`开始删除密钥，keyId: ${keyId}`)

    // 检查 Supabase 连接
    console.log('检查 Supabase 连接状态...')
    
    // 首先查询密钥是否存在
    const { data: key, error: keyError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('id, key_code, tutorial_id, status')
      .eq('id', keyId)
      .single()

    if (keyError) {
      console.error("❌ 查询密钥错误:", keyError)
      return NextResponse.json({ 
        error: "查询密钥失败", 
        details: keyError.message,
        code: keyError.code 
      }, { status: 500 })
    }

    if (!key) {
      console.log(`❌ 密钥不存在，keyId: ${keyId}`)
      return NextResponse.json({ error: "密钥不存在" }, { status: 404 })
    }

    console.log(`✅ 找到密钥: ${key.key_code}, 状态: ${key.status}, 教程ID: ${key.tutorial_id}`)

    // 先查询是否有相关的解锁记录
    const { data: unlocks, error: queryUnlocksError } = await supabaseAdmin
      .from('user_unlocks')
      .select('id, user_identifier')
      .eq('key_id', keyId)

    if (queryUnlocksError) {
      console.error("❌ 查询解锁记录错误:", queryUnlocksError)
      return NextResponse.json({ 
        error: "查询解锁记录失败", 
        details: queryUnlocksError.message 
      }, { status: 500 })
    }

    console.log(`📊 找到 ${unlocks?.length || 0} 条相关解锁记录`)

    // 删除相关的用户解锁记录（如果存在）
    if (unlocks && unlocks.length > 0) {
      console.log(`开始删除 ${unlocks.length} 条解锁记录...`)
      const { error: deleteUnlocksError } = await supabaseAdmin
        .from('user_unlocks')
        .delete()
        .eq('key_id', keyId)

      if (deleteUnlocksError) {
        console.error("❌ 删除解锁记录错误:", deleteUnlocksError)
        return NextResponse.json({ 
          error: "删除解锁记录失败",
          details: deleteUnlocksError.message 
        }, { status: 500 })
      }

      console.log(`✅ 成功删除 ${unlocks.length} 条解锁记录`)
    }

    // 删除密钥记录
    console.log('开始删除密钥记录...')
    const { error: deleteKeyError } = await supabaseAdmin
      .from('tutorial_keys')
      .delete()
      .eq('id', keyId)

    if (deleteKeyError) {
      console.error("❌ 删除密钥错误:", deleteKeyError)
      return NextResponse.json({ 
        error: "删除密钥失败",
        details: deleteKeyError.message,
        code: deleteKeyError.code
      }, { status: 500 })
    }

    console.log(`✅ 成功删除密钥: ${key.key_code}`)
    console.log('=== 密钥删除操作完成 ===')

    return NextResponse.json({
      success: true,
      message: "密钥删除成功",
      data: {
        deleted_key: key.key_code,
        tutorial_id: key.tutorial_id,
        deleted_unlocks_count: unlocks?.length || 0
      }
    })

  } catch (error) {
    console.error("💥 删除密钥异常:", error)
    console.error("错误详情:", {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    })
    
    return NextResponse.json({ 
      error: "服务器错误",
      details: error instanceof Error ? error.message : "未知错误",
      type: error instanceof Error ? error.constructor.name : 'Unknown'
    }, { status: 500 })
  }
}
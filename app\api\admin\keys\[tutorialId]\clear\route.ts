import { type NextRequest, NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

interface RouteParams {
  params: {
    tutorialId: string
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    console.log('=== 开始清除密钥操作 ===')
    
    const tutorialId = parseInt(params.tutorialId)
    
    console.log('请求参数:', { tutorialId, originalParam: params.tutorialId })
    
    if (!tutorialId) {
      console.log('❌ 无效的教程ID:', params.tutorialId)
      return NextResponse.json({ error: "无效的教程ID" }, { status: 400 })
    }

    // 检查 Supabase 连接
    console.log('检查 Supabase 连接状态...')

    // 检查教程是否存在
    console.log('查询教程信息...')
    const { data: tutorial, error: tutorialError } = await supabaseAdmin
      .from('tutorials')
      .select('id, title')
      .eq('id', tutorialId)
      .single()

    if (tutorialError || !tutorial) {
      console.error('❌ 查询教程失败:', tutorialError)
      return NextResponse.json({ 
        error: "教程不存在", 
        details: tutorialError?.message 
      }, { status: 404 })
    }

    console.log(`✅ 找到教程: ${tutorial.title} (ID: ${tutorial.id})`)

    // 检查是否有已使用的密钥（防止误删除）
    console.log('检查已使用的密钥...')
    const { data: usedKeys, error: usedKeysError } = await supabaseAdmin
      .from('tutorial_keys')
      .select('*', { count: 'exact', head: true })
      .eq('tutorial_id', tutorialId)
      .eq('status', 'used')

    if (usedKeysError) {
      console.error("❌ 检查已使用密钥错误:", usedKeysError)
      return NextResponse.json({ 
        error: "检查密钥状态时出错", 
        details: usedKeysError.message 
      }, { status: 500 })
    }

    const usedKeyCount = usedKeys?.count || 0
    console.log(`📊 已使用密钥数量: ${usedKeyCount}`)

    // 删除所有未使用和已导出的密钥，保留已使用的密钥
    console.log('开始删除未使用和已导出的密钥...')
    const { data: deletedKeys, error: deleteError } = await supabaseAdmin
      .from('tutorial_keys')
      .delete()
      .eq('tutorial_id', tutorialId)
      .in('status', ['unused', 'exported'])
      .select('*', { count: 'exact' })

    if (deleteError) {
      console.error("❌ 删除密钥错误:", deleteError)
      return NextResponse.json({ 
        error: "清除密钥失败", 
        details: deleteError.message,
        code: deleteError.code 
      }, { status: 500 })
    }

    const deletedCount = deletedKeys?.length || 0
    console.log(`✅ 成功删除 ${deletedCount} 个密钥`)
    console.log('=== 清除密钥操作完成 ===')

    return NextResponse.json({
      success: true,
      message: `成功清除 ${deletedCount} 个密钥`,
      data: {
        tutorial_id: tutorialId,
        tutorial_title: tutorial.title,
        deleted_count: deletedCount,
        preserved_used_keys: usedKeyCount
      }
    })

  } catch (error) {
    console.error("💥 清除密钥异常:", error)
    console.error("错误详情:", {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    })
    
    return NextResponse.json({ 
      error: "服务器内部错误",
      details: error instanceof Error ? error.message : "未知错误",
      type: error instanceof Error ? error.constructor.name : 'Unknown'
    }, { status: 500 })
  }
}
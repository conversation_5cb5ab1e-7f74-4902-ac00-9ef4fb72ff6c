import { NextResponse } from "next/server"
import { supabaseAdmin } from "@/lib/supabase"

export async function GET() {
  try {
    console.log('=== API健康检查开始 ===')
    
    // 1. 检查环境变量
    const envCheck = {
      supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      supabaseServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    }
    console.log('环境变量检查:', envCheck)
    
    // 2. 检查Supabase连接
    const { data: connection, error: connectionError } = await supabaseAdmin
      .from('tutorials')
      .select('count', { count: 'exact', head: true })
      .limit(1)
    
    if (connectionError) {
      console.error('Supabase连接错误:', connectionError)
      return NextResponse.json({
        status: 'error',
        message: 'Supabase连接失败',
        details: {
          env: envCheck,
          error: connectionError.message,
          code: connectionError.code
        }
      }, { status: 500 })
    }
    
    // 3. 检查表结构
    const { data: tables, error: tablesError } = await supabaseAdmin.rpc('check_table_exists', {
      table_name: 'tutorial_keys'
    }).then(() => ({ data: true, error: null })).catch((err) => ({ data: false, error: err }))
    
    console.log('=== API健康检查完成 ===')
    
    return NextResponse.json({
      status: 'healthy',
      message: 'API工作正常',
      details: {
        env: envCheck,
        supabase: {
          connected: true,
          tutorialsCount: connection?.count || 0
        },
        timestamp: new Date().toISOString()
      }
    })
    
  } catch (error) {
    console.error('健康检查异常:', error)
    
    return NextResponse.json({
      status: 'error',
      message: '健康检查失败',
      details: {
        error: error instanceof Error ? error.message : '未知错误',
        type: error instanceof Error ? error.constructor.name : 'Unknown'
      }
    }, { status: 500 })
  }
}